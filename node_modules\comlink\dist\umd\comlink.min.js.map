{"version": 3, "file": "comlink.min.js", "sources": ["../../src/comlink.ts"], "sourcesContent": [null], "names": ["proxy<PERSON><PERSON><PERSON>", "Symbol", "createEndpoint", "releaseProxy", "finalizer", "<PERSON><PERSON><PERSON><PERSON>", "isObject", "val", "transferHandlers", "Map", "canHandle", "serialize", "obj", "port1", "port2", "MessageChannel", "expose", "deserialize", "port", "start", "wrap", "value", "serialized", "Error", "isError", "message", "name", "stack", "Object", "assign", "ep", "globalThis", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "callback", "ev", "data", "origin", "<PERSON><PERSON><PERSON><PERSON>", "RegExp", "test", "isAllowedOrigin", "console", "warn", "id", "type", "path", "argumentList", "map", "fromWireValue", "returnValue", "parent", "slice", "reduce", "prop", "rawValue", "apply", "proxy", "transfer", "undefined", "Promise", "resolve", "catch", "then", "wireValue", "transferables", "toWireValue", "postMessage", "removeEventListener", "closeEndPoint", "error", "TypeError", "endpoint", "constructor", "isMessagePort", "close", "target", "pendingListeners", "resolver", "get", "delete", "createProxy", "throwIfProxyReleased", "isReleased", "releaseEndpoint", "requestResponseMessage", "proxyCounter", "WeakMap", "proxyFinalizers", "FinalizationRegistry", "newCount", "set", "isProxyReleased", "Proxy", "_target", "unregister", "unregisterProxy", "clear", "length", "r", "p", "toString", "bind", "_thisArg", "rawArgumentList", "last", "processArguments", "construct", "register", "registerProxy", "processed", "v", "arr", "Array", "prototype", "concat", "transferCache", "transfers", "handler", "serializedValue", "msg", "fill", "Math", "floor", "random", "Number", "MAX_SAFE_INTEGER", "join", "w", "context", "target<PERSON>rigin"], "mappings": ";;;;;aAiBaA,EAAcC,OAAO,iBACrBC,EAAiBD,OAAO,oBACxBE,EAAeF,OAAO,wBACtBG,EAAYH,OAAO,qBAE1BI,EAAcJ,OAAO,kBAuJrBK,EAAYC,GACA,iBAARA,GAA4B,OAARA,GAAgC,mBAARA,EAmGzCC,EAAmB,IAAIC,IAGlC,CACA,CAAC,QArEgE,CACjEC,UAAYH,GACVD,EAASC,IAASA,EAAoBP,GACxC,SAAAW,CAAUC,GACR,MAAMC,MAAEA,EAAKC,MAAEA,GAAU,IAAIC,eAE7B,OADAC,EAAOJ,EAAKC,GACL,CAACC,EAAO,CAACA,GACjB,EACDG,YAAYC,IACVA,EAAKC,QACEC,EAAKF,MA4Dd,CAAC,QAtCC,CACFR,UAAYW,GACVf,EAASe,IAAUhB,KAAegB,EACpC,SAAAV,EAAUU,MAAEA,IACV,IAAIC,EAaJ,OAXEA,EADED,aAAiBE,MACN,CACXC,SAAS,EACTH,MAAO,CACLI,QAASJ,EAAMI,QACfC,KAAML,EAAMK,KACZC,MAAON,EAAMM,QAIJ,CAAEH,SAAS,EAAOH,SAE1B,CAACC,EAAY,GACrB,EACD,WAAAL,CAAYK,GACV,GAAIA,EAAWE,QACb,MAAMI,OAAOC,OACX,IAAIN,MAAMD,EAAWD,MAAMI,SAC3BH,EAAWD,OAGf,MAAMC,EAAWD,KAClB,MA6BG,SAAUL,EACdJ,EACAkB,EAAeC,WACfC,EAAsC,CAAC,MAEvCF,EAAGG,iBAAiB,WAAW,SAASC,EAASC,GAC/C,IAAKA,IAAOA,EAAGC,KACb,OAEF,IAxBJ,SACEJ,EACAK,GAEA,IAAK,MAAMC,KAAiBN,EAAgB,CAC1C,GAAIK,IAAWC,GAAmC,MAAlBA,EAC9B,OAAO,EAET,GAAIA,aAAyBC,QAAUD,EAAcE,KAAKH,GACxD,OAAO,CAEV,CACD,OAAO,CACT,CAWSI,CAAgBT,EAAgBG,EAAGE,QAEtC,YADAK,QAAQC,KAAK,mBAAmBR,EAAGE,6BAGrC,MAAMO,GAAEA,EAAEC,KAAEA,EAAIC,KAAEA,GAAMlB,OAAAC,OAAA,CACtBiB,KAAM,IACFX,EAAGC,MAEHW,GAAgBZ,EAAGC,KAAKW,cAAgB,IAAIC,IAAIC,GACtD,IAAIC,EACJ,IACE,MAAMC,EAASL,EAAKM,MAAM,GAAI,GAAGC,QAAO,CAACzC,EAAK0C,IAAS1C,EAAI0C,IAAO1C,GAC5D2C,EAAWT,EAAKO,QAAO,CAACzC,EAAK0C,IAAS1C,EAAI0C,IAAO1C,GACvD,OAAQiC,GACN,IAAA,MAEIK,EAAcK,EAEhB,MACF,IAAA,MAEIJ,EAAOL,EAAKM,OAAO,GAAG,IAAMH,EAAcd,EAAGC,KAAKf,OAClD6B,GAAc,EAEhB,MACF,IAAA,QAEIA,EAAcK,EAASC,MAAML,EAAQJ,GAEvC,MACF,IAAA,YAGIG,EAAcO,EADA,IAAIF,KAAYR,IAGhC,MACF,IAAA,WACE,CACE,MAAMlC,MAAEA,EAAKC,MAAEA,GAAU,IAAIC,eAC7BC,EAAOJ,EAAKE,GACZoC,EAAcQ,EAAS7C,EAAO,CAACA,GAChC,CACD,MACF,IAAA,UAEIqC,OAAcS,EAEhB,MACF,QACE,OAEL,CAAC,MAAOtC,GACP6B,EAAc,CAAE7B,QAAOhB,CAACA,GAAc,EACvC,CACDuD,QAAQC,QAAQX,GACbY,OAAOzC,IACC,CAAEA,QAAOhB,CAACA,GAAc,MAEhC0D,MAAMb,IACL,MAAOc,EAAWC,GAAiBC,EAAYhB,GAC/CpB,EAAGqC,YAAiBvC,OAAAC,OAAAD,OAAAC,OAAA,GAAAmC,IAAWpB,OAAMqB,eACjCpB,IAEFf,EAAGsC,oBAAoB,UAAWlC,GAClCmC,EAAcvC,GACV1B,KAAaQ,GAAiC,mBAAnBA,EAAIR,IACjCQ,EAAIR,KAEP,IAEF0D,OAAOQ,IAEN,MAAON,EAAWC,GAAiBC,EAAY,CAC7C7C,MAAO,IAAIkD,UAAU,+BACrBlE,CAACA,GAAc,IAEjByB,EAAGqC,YAAiBvC,OAAAC,OAAAD,OAAAC,OAAA,GAAAmC,IAAWpB,OAAMqB,EAAc,GAEzD,IACInC,EAAGX,OACLW,EAAGX,OAEP,CAMA,SAASkD,EAAcG,IAJvB,SAAuBA,GACrB,MAAqC,gBAA9BA,EAASC,YAAY/C,IAC9B,EAGMgD,CAAcF,IAAWA,EAASG,OACxC,CAEgB,SAAAvD,EAAQU,EAAc8C,GACpC,MAAMC,EAAyC,IAAIpE,IAmBnD,OAjBAqB,EAAGG,iBAAiB,WAAW,SAAuBE,GACpD,MAAMC,KAAEA,GAASD,EACjB,IAAKC,IAASA,EAAKQ,GACjB,OAEF,MAAMkC,EAAWD,EAAiBE,IAAI3C,EAAKQ,IAC3C,GAAKkC,EAIL,IACEA,EAAS1C,EACV,CAAS,QACRyC,EAAiBG,OAAO5C,EAAKQ,GAC9B,CACH,IAEOqC,EAAenD,EAAI+C,EAAkB,GAAID,EAClD,CAEA,SAASM,EAAqBC,GAC5B,GAAIA,EACF,MAAM,IAAI5D,MAAM,6CAEpB,CAEA,SAAS6D,EAAgBtD,GACvB,OAAOuD,EAAuBvD,EAAI,IAAIrB,IAAO,CAC3CoC,KAAyB,YACxBkB,MAAK,KACNM,EAAcvC,EAAG,GAErB,CAaA,MAAMwD,EAAe,IAAIC,QACnBC,EACJ,yBAA0BzD,YAC1B,IAAI0D,sBAAsB3D,IACxB,MAAM4D,GAAYJ,EAAaP,IAAIjD,IAAO,GAAK,EAC/CwD,EAAaK,IAAI7D,EAAI4D,GACJ,IAAbA,GACFN,EAAgBtD,EACjB,IAiBL,SAASmD,EACPnD,EACA+C,EACA/B,EAAqC,GACrC8B,EAAiB,cAEjB,IAAIgB,GAAkB,EACtB,MAAMnC,EAAQ,IAAIoC,MAAMjB,EAAQ,CAC9B,GAAAG,CAAIe,EAASxC,GAEX,GADA4B,EAAqBU,GACjBtC,IAASnD,EACX,MAAO,MAjBf,SAAyBsD,GACnB+B,GACFA,EAAgBO,WAAWtC,EAE/B,CAcUuC,CAAgBvC,GAChB2B,EAAgBtD,GAChB+C,EAAiBoB,QACjBL,GAAkB,CAAI,EAG1B,GAAa,SAATtC,EAAiB,CACnB,GAAoB,IAAhBR,EAAKoD,OACP,MAAO,CAAEnC,KAAM,IAAMN,GAEvB,MAAM0C,EAAId,EAAuBvD,EAAI+C,EAAkB,CACrDhC,KAAqB,MACrBC,KAAMA,EAAKE,KAAKoD,GAAMA,EAAEC,eACvBtC,KAAKd,GACR,OAAOkD,EAAEpC,KAAKuC,KAAKH,EACpB,CACD,OAAOlB,EAAYnD,EAAI+C,EAAkB,IAAI/B,EAAMQ,GACpD,EACD,GAAAqC,CAAIG,EAASxC,EAAMC,GACjB2B,EAAqBU,GAGrB,MAAOvE,EAAO4C,GAAiBC,EAAYX,GAC3C,OAAO8B,EACLvD,EACA+C,EACA,CACEhC,KAAqB,MACrBC,KAAM,IAAIA,EAAMQ,GAAMN,KAAKoD,GAAMA,EAAEC,aACnChF,SAEF4C,GACAF,KAAKd,EACR,EACD,KAAAO,CAAMsC,EAASS,EAAUC,GACvBtB,EAAqBU,GACrB,MAAMa,EAAO3D,EAAKA,EAAKoD,OAAS,GAChC,GAAKO,IAAiBvG,EACpB,OAAOmF,EAAuBvD,EAAI+C,EAAkB,CAClDhC,KAA0B,aACzBkB,KAAKd,GAGV,GAAa,SAATwD,EACF,OAAOxB,EAAYnD,EAAI+C,EAAkB/B,EAAKM,MAAM,GAAI,IAE1D,MAAOL,EAAckB,GAAiByC,EAAiBF,GACvD,OAAOnB,EACLvD,EACA+C,EACA,CACEhC,KAAuB,QACvBC,KAAMA,EAAKE,KAAKoD,GAAMA,EAAEC,aACxBtD,gBAEFkB,GACAF,KAAKd,EACR,EACD,SAAA0D,CAAUb,EAASU,GACjBtB,EAAqBU,GACrB,MAAO7C,EAAckB,GAAiByC,EAAiBF,GACvD,OAAOnB,EACLvD,EACA+C,EACA,CACEhC,KAA2B,YAC3BC,KAAMA,EAAKE,KAAKoD,GAAMA,EAAEC,aACxBtD,gBAEFkB,GACAF,KAAKd,EACR,IAGH,OApGF,SAAuBQ,EAAe3B,GACpC,MAAM4D,GAAYJ,EAAaP,IAAIjD,IAAO,GAAK,EAC/CwD,EAAaK,IAAI7D,EAAI4D,GACjBF,GACFA,EAAgBoB,SAASnD,EAAO3B,EAAI2B,EAExC,CA6FEoD,CAAcpD,EAAO3B,GACd2B,CACT,CAMA,SAASiD,EAAiB3D,GACxB,MAAM+D,EAAY/D,EAAaC,IAAIkB,GACnC,MAAO,CAAC4C,EAAU9D,KAAK+D,GAAMA,EAAE,MANdC,EAM0BF,EAAU9D,KAAK+D,GAAMA,EAAE,KAL3DE,MAAMC,UAAUC,OAAO3D,MAAM,GAAIwD,KAD1C,IAAmBA,CAOnB,CAEA,MAAMI,EAAgB,IAAI7B,QACV,SAAA7B,EAAY9C,EAAQyG,GAElC,OADAD,EAAczB,IAAI/E,EAAKyG,GAChBzG,CACT,CAEM,SAAU6C,EAAoB7C,GAClC,OAAOgB,OAAOC,OAAOjB,EAAK,CAAEZ,CAACA,IAAc,GAC7C,CAeA,SAASkE,EAAY7C,GACnB,IAAK,MAAOK,EAAM4F,KAAY9G,EAC5B,GAAI8G,EAAQ5G,UAAUW,GAAQ,CAC5B,MAAOkG,EAAiBtD,GAAiBqD,EAAQ3G,UAAUU,GAC3D,MAAO,CACL,CACEwB,KAA2B,UAC3BnB,OACAL,MAAOkG,GAETtD,EAEH,CAEH,MAAO,CACL,CACEpB,KAAuB,MACvBxB,SAEF+F,EAAcrC,IAAI1D,IAAU,GAEhC,CAEA,SAAS4B,EAAc5B,GACrB,OAAQA,EAAMwB,MACZ,IAAA,UACE,OAAOrC,EAAiBuE,IAAI1D,EAAMK,MAAOT,YAAYI,EAAMA,OAC7D,IAAA,MACE,OAAOA,EAAMA,MAEnB,CAEA,SAASgE,EACPvD,EACA+C,EACA2C,EACAH,GAEA,OAAO,IAAIzD,SAASC,IAClB,MAAMjB,EAUD,IAAIqE,MAAM,GACdQ,KAAK,GACLzE,KAAI,IAAM0E,KAAKC,MAAMD,KAAKE,SAAWC,OAAOC,kBAAkBzB,SAAS,MACvE0B,KAAK,KAZNlD,EAAiBc,IAAI/C,EAAIiB,GACrB/B,EAAGX,OACLW,EAAGX,QAELW,EAAGqC,YAAcvC,OAAAC,OAAA,CAAAe,MAAO4E,GAAOH,EAAU,GAE7C,2IA3DM,SACJW,EACAC,EAAuBlG,WACvBmG,EAAe,KAEf,MAAO,CACL/D,YAAa,CAACqD,EAAUvD,IACtB+D,EAAE7D,YAAYqD,EAAKU,EAAcjE,GACnChC,iBAAkBgG,EAAQhG,iBAAiBqE,KAAK2B,GAChD7D,oBAAqB6D,EAAQ7D,oBAAoBkC,KAAK2B,GAE1D"}