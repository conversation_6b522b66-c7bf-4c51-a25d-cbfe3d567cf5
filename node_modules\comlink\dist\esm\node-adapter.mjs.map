{"version": 3, "file": "node-adapter.mjs", "sources": ["../../src/node-adapter.ts"], "sourcesContent": [null], "names": [], "mappings": "AAAA;;;;AAIG;AAmBqB,SAAA,YAAY,CAAC,GAAiB,EAAA;AACpD,IAAA,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;IAChC,OAAO;QACL,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;AACtC,QAAA,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,KAAI;AAC1B,YAAA,MAAM,CAAC,GAAG,CAAC,IAAS,KAAI;gBACtB,IAAI,aAAa,IAAI,EAAE,EAAE;AACvB,oBAAA,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAkB,CAAC,CAAC;AAC1C,iBAAA;AAAM,qBAAA;AACL,oBAAA,EAAE,CAAC,EAAE,IAAI,EAAkB,CAAC,CAAC;AAC9B,iBAAA;AACH,aAAC,CAAC;AACF,YAAA,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACrB,YAAA,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SACtB;AACD,QAAA,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,KAAI;YAC7B,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,EAAE;gBACN,OAAO;AACR,aAAA;AACD,YAAA,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACtB,YAAA,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACtB;AACD,QAAA,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;KACxC,CAAC;AACJ;;;;"}