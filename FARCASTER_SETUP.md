# 🎯 Farcaster Mini App Setup & Validation Guide

## 🛠️ Farcaster Developer Tools & Manifest Validation

### **Enable Developer Mode**
1. Go to: https://farcaster.xyz/~/settings/developer-tools
2. Toggle "Developer Mode" ON
3. Access developer tools on desktop sidebar

### **Manifest Validation Tools** 🔧

#### **Option 1: Farcaster Hosted Manifests (Recommended)**
- **URL**: https://farcaster.xyz/~/developers/mini-apps/manifest
- **Benefits**:
  - ✅ No need to manage manifest files in codebase
  - ✅ Update manifest without redeploying
  - ✅ Automatic validation and error checking
  - ✅ Easy domain migration support

**Setup Steps:**
1. Go to manifest tool URL above
2. Enter your domain and app details
3. Get hosted manifest ID (e.g., `1234567890`)
4. Set up redirect in your app:

```javascript
// For Express.js
app.get('/.well-known/farcaster.json', (req, res) => {
  res.redirect(307, 'https://api.farcaster.xyz/miniapps/hosted-manifest/YOUR_ID')
})

// For Next.js (next.config.js)
async redirects() {
  return [
    {
      source: '/.well-known/farcaster.json',
      destination: 'https://api.farcaster.xyz/miniapps/hosted-manifest/YOUR_ID',
      permanent: false,
    },
  ]
}
```

#### **Option 2: Self-Hosted Manifest**
- Host `/.well-known/farcaster.json` on your domain
- Use the file we created in `.well-known/farcaster.json`
- Update URLs to match your deployed domain

### **Validation & Debug Tools** 🧪

#### **1. Embed Debugging Tool**
- **URL**: https://farcaster.xyz/~/developers/frames/embed-debugger
- **Use**: Test your Mini App embed preview
- **What it checks**:
  - Manifest file accessibility
  - Required fields validation
  - Image URL validation
  - Icon format and size

#### **2. Frame Developer Tool**
- **URL**: https://farcaster.xyz/~/developers/frames
- **Use**: General frame/mini app debugging
- **Features**:
  - Preview how your app appears in feed
  - Test sharing functionality
  - Validate metadata

#### **3. Mini App Manifest Tool**
- **URL**: https://farcaster.xyz/~/developers/new
- **Use**: Generate account association for verification
- **Process**:
  1. Enter your domain
  2. Sign with your Farcaster account
  3. Get cryptographic signature
  4. Add to manifest for verification

## 📋 Manifest Configuration Checklist

### **Required Fields** ✅
- [ ] `version`: "1"
- [ ] `name`: "Which Crypto Leader Are You?" (max 32 chars)
- [ ] `iconUrl`: 1024x1024px PNG, no alpha
- [ ] `homeUrl`: Your deployed app URL

### **Recommended Fields** 📝
- [ ] `subtitle`: "Discover your Web3 personality" (max 30 chars)
- [ ] `description`: Full description (max 170 chars)
- [ ] `splashImageUrl`: 200x200px loading image
- [ ] `splashBackgroundColor`: Hex color code
- [ ] `primaryCategory`: "entertainment"
- [ ] `tags`: ["quiz", "personality", "crypto", "web3", "leaders"]

### **Optional but Good** 🎨
- [ ] `screenshotUrls`: Up to 3 portrait screenshots (1284x2778)
- [ ] `heroImageUrl`: 1200x630px promotional image
- [ ] `ogTitle`, `ogDescription`, `ogImageUrl`: Social sharing
- [ ] `requiredCapabilities`: SDK methods your app uses

## 🔍 Testing Your Mini App

### **Local Testing with ngrok**
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
npx ngrok http 3000

# Use the https URL for testing in Farcaster
```

### **Production Testing Steps**
1. **Deploy to Vercel/Netlify**
2. **Update manifest URLs** to match your domain
3. **Test manifest accessibility**:
   ```bash
   curl https://your-domain.com/.well-known/farcaster.json
   ```
4. **Validate in Farcaster tools**
5. **Test in mobile Farcaster app**

### **Common Issues & Solutions** 🐛

#### **Manifest not found (404)**
```bash
# Check if file exists and is accessible
curl -I https://your-domain.com/.well-known/farcaster.json
# Should return 200 OK
```

#### **CORS issues**
- Ensure your server allows cross-origin requests
- Check `vercel.json` or `netlify.toml` headers

#### **Icon/Image issues**
- Icons must be exactly 1024x1024px PNG
- No transparency (alpha channel)
- Accessible via HTTPS

#### **SDK not initializing**
- Must be served over HTTPS
- Check browser console for errors
- Ensure `sdk.actions.ready()` is called

## 🚀 Deployment Checklist

### **Before Going Live**
- [ ] Test locally with `npm run dev`
- [ ] All images load correctly
- [ ] Quiz logic works end-to-end
- [ ] Share functionality tested
- [ ] Mobile responsive design verified
- [ ] Manifest file accessible

### **After Deployment**
- [ ] Update manifest URLs to production domain
- [ ] Test manifest validation tools
- [ ] Verify in Farcaster mobile app
- [ ] Submit for verification (optional)
- [ ] Monitor for errors

### **Account Verification (Optional)**
For verified badge and developer rewards:
1. Use Mini App Manifest Tool to generate signature
2. Add `accountAssociation` to your manifest
3. Domain must exactly match the tool input

## 📱 Mobile Testing Tips

1. **Use actual mobile device** - Desktop preview isn't enough
2. **Test in Farcaster app** - Not just mobile browser
3. **Check loading speed** - Should load in < 2 seconds
4. **Verify touch interactions** - Buttons, scrolling, etc.
5. **Test share functionality** - Copy to clipboard fallback

## 🎯 Success Metrics to Track

After going live, monitor:
- **Load time** < 2 seconds
- **Quiz completion rate** > 80%
- **Share rate** > 30%
- **Error rate** < 1%
- **Mobile usage** (likely 90%+ of traffic)

---

**Ready to deploy?** Follow this checklist and your Mini App will be ready for the Farcaster ecosystem! 🚀
