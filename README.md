# 🧠 Which Crypto Leader Are You?

A Farcaster Mini App quiz that matches users with famous crypto personalities based on their answers to 8 personality questions.

## 🎯 Features

- **8 Personality Questions** - Web3-relevant questions that feel natural and relatable
- **5 Character Results** - Match with <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, or <PERSON>
- **Beautiful UI** - Mobile-first design with smooth animations
- **Social Sharing** - One-click sharing to Warpcast with custom captions
- **Farcaster Integration** - Built with official Mini App SDK

## 🚀 Quick Start

### Prerequisites

- Node.js 22.11.0 or higher
- npm, pnpm, or yarn

### Installation

1. **Clone/Download the project**

```bash
# If you have git
git clone <your-repo-url>
cd crypto-leader-quiz

# Or just download and extract the files
```

2. **Install dependencies**

```bash
npm install
```

3. **Run locally**

```bash
npm run dev
```

4. **Open in browser**

```
http://localhost:3000
```

## 🧪 Testing & Debugging

### Local Testing

1. Run `npm run dev`
2. Open `http://localhost:3000` in browser
3. Test all quiz flows:
   - Start screen → Quiz → Results → Share → Restart

### Farcaster Testing

1. Enable Developer Mode in Farcaster:

   - Go to: https://farcaster.xyz/~/settings/developer-tools
   - Toggle "Developer Mode" ON

2. Test in Farcaster environment:
   - Use ngrok or similar to expose local server
   - Test Mini App SDK integration
   - Verify sharing functionality

### Debug Checklist

- [ ] All images load correctly from `Gambar/` folder
- [ ] Quiz logic works (8 questions, proper scoring)
- [ ] All 5 character results display properly
- [ ] Share functionality works
- [ ] Mobile responsive design
- [ ] Mini App SDK initializes without errors

## 📱 Deployment

### Option 1: Vercel (Recommended) 🚀

1. **Push to GitHub:**

   ```bash
   git init
   git add .
   git commit -m "Initial commit: Crypto Leader Quiz Mini App"
   git branch -M main
   git remote add origin https://github.com/yourusername/crypto-leader-quiz.git
   git push -u origin main
   ```

2. **Deploy to Vercel:**

   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Vercel will auto-detect settings from `vercel.json`
   - Click "Deploy"
   - Get your live URL: `https://your-app.vercel.app`

3. **Configure for Farcaster:**
   - Copy your Vercel URL
   - Test in Farcaster Developer Tools
   - Submit to Mini App directory

### Option 2: Netlify 🌐

1. **Drag & Drop Method:**

   - Go to [netlify.com](https://netlify.com)
   - Drag your project folder to deploy area
   - Get instant URL: `https://random-name.netlify.app`

2. **GitHub Integration:**
   - Connect your GitHub repository
   - Netlify reads `netlify.toml` automatically
   - Auto-deploy on every push

### Option 3: GitHub Pages 📄

1. **Enable GitHub Pages:**
   ```bash
   git checkout -b gh-pages
   git push origin gh-pages
   ```
   - Go to repository Settings → Pages
   - Select `gh-pages` branch
   - Get URL: `https://yourusername.github.io/crypto-leader-quiz`

### 🔧 Post-Deployment Checklist

- [ ] Test all quiz functionality
- [ ] Verify images load correctly
- [ ] Test sharing functionality
- [ ] Check mobile responsiveness
- [ ] Test in Farcaster environment
- [ ] Submit to Farcaster Mini App directory

## 🔧 Configuration

### Update Mini App Details

Edit `manifest.json`:

```json
{
  "name": "Your App Name",
  "description": "Your description",
  "miniapp": {
    "author": "Your Name",
    "homepage": "https://your-domain.com"
  }
}
```

### Customize Questions

Edit the `questions` array in `script.js` to add/modify questions.

### Add New Characters

Add new character objects to the `characters` object in `script.js`.

## 📊 Quiz Structure

### Questions (8 total)

Each question has 3 answers that map to different personality types:

- **Type A** → Introverted, ideological (Satoshi-like)
- **Type B** → Innovative, systematic (Vitalik-like)
- **Type C** → Business-focused, pragmatic (CZ/Brian/Jack-like)

### Results (5 characters)

- 🦉 **SATOSHINATOR** - The Crypto Ghost
- 🧠 **VITALIK VIBES** - The Ethereum Architect
- ⚡ **CZ ENERGY** - The Binance Builder
- 🏦 **BRIAN BLUEPRINT** - The Coinbase Captain
- 🐦 **DORSEY DECODED** - The Bitcoin Maximalist

## 🎨 Customization

### Styling

- Edit `styles.css` for visual changes
- Gradient colors: `#667eea` to `#764ba2`
- Mobile-first responsive design

### Images

- Replace images in `Gambar/` folder
- Supported formats: PNG, JPG
- Recommended size: 400x400px minimum

### Share Messages

Each character has a unique share message template in `script.js` under the `shareText` property.

## 🐛 Common Issues

### "Mini App won't load"

- Check Node.js version (must be 22.11.0+)
- Verify all dependencies installed
- Check browser console for errors

### "Images not showing"

- Verify image files exist in `Gambar/` folder
- Check file extensions match code (.png vs .jpg)
- Ensure proper file permissions

### "Share not working"

- Test on actual mobile device
- Verify Mini App SDK initialization
- Check browser permissions for clipboard

## 📚 Resources

- [Farcaster Mini Apps Docs](https://miniapps.farcaster.xyz)
- [Mini App SDK Reference](https://miniapps.farcaster.xyz/docs/sdk)
- [Developer Tools](https://farcaster.xyz/~/settings/developer-tools)
- [Farcaster Developer Community](https://warpcast.com/~/channel/fc-devs)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

MIT License - feel free to use and modify!

---

**Ready to deploy?** Follow the deployment steps above and share your Mini App with the Farcaster community! 🚀
