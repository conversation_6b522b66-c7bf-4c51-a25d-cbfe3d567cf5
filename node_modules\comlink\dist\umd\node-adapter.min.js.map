{"version": 3, "file": "node-adapter.min.js", "sources": ["../../src/node-adapter.ts"], "sourcesContent": [null], "names": ["nep", "listeners", "WeakMap", "postMessage", "bind", "addEventListener", "_", "eh", "l", "data", "handleEvent", "on", "set", "removeEventListener", "get", "off", "delete", "start"], "mappings": ";;;;;cAuBwB,SAAaA,GACnC,MAAMC,EAAY,IAAIC,QACtB,MAAO,CACLC,YAAaH,EAAIG,YAAYC,KAAKJ,GAClCK,iBAAkB,CAACC,EAAGC,KACpB,MAAMC,EAAKC,IACL,gBAAiBF,EACnBA,EAAGG,YAAY,CAAED,SAEjBF,EAAG,CAAEE,QACN,EAEHT,EAAIW,GAAG,UAAWH,GAClBP,EAAUW,IAAIL,EAAIC,EAAE,EAEtBK,oBAAqB,CAACP,EAAGC,KACvB,MAAMC,EAAIP,EAAUa,IAAIP,GACnBC,IAGLR,EAAIe,IAAI,UAAWP,GACnBP,EAAUe,OAAOT,GAAG,EAEtBU,MAAOjB,EAAIiB,OAASjB,EAAIiB,MAAMb,KAAKJ,GAEvC"}