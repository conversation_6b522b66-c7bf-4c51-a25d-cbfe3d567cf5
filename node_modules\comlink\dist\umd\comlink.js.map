{"version": 3, "file": "comlink.js", "sources": ["../../src/comlink.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;IAAA;;;;IAIG;UAaU,WAAW,GAAG,MAAM,CAAC,eAAe,EAAE;UACtC,cAAc,GAAG,MAAM,CAAC,kBAAkB,EAAE;UAC5C,YAAY,GAAG,MAAM,CAAC,sBAAsB,EAAE;UAC9C,SAAS,GAAG,MAAM,CAAC,mBAAmB,EAAE;IAErD,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAuJ7C,MAAM,QAAQ,GAAG,CAAC,GAAY,KAC5B,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,UAAU,CAAC;IA+BzE;;IAEG;IACH,MAAM,oBAAoB,GAAyC;IACjE,IAAA,SAAS,EAAE,CAAC,GAAG,KACb,QAAQ,CAAC,GAAG,CAAC,IAAK,GAAmB,CAAC,WAAW,CAAC;IACpD,IAAA,SAAS,CAAC,GAAG,EAAA;YACX,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC;IAC9C,QAAA,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACnB,QAAA,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;SACzB;IACD,IAAA,WAAW,CAAC,IAAI,EAAA;YACd,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;SACnB;KACF,CAAC;IAcF;;IAEG;IACH,MAAM,oBAAoB,GAGtB;IACF,IAAA,SAAS,EAAE,CAAC,KAAK,KACf,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,IAAI,KAAK;QACzC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAA;IACjB,QAAA,IAAI,UAAiC,CAAC;YACtC,IAAI,KAAK,YAAY,KAAK,EAAE;IAC1B,YAAA,UAAU,GAAG;IACX,gBAAA,OAAO,EAAE,IAAI;IACb,gBAAA,KAAK,EAAE;wBACL,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;IACnB,iBAAA;iBACF,CAAC;IACH,SAAA;IAAM,aAAA;gBACL,UAAU,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IACxC,SAAA;IACD,QAAA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;SACzB;IACD,IAAA,WAAW,CAAC,UAAU,EAAA;YACpB,IAAI,UAAU,CAAC,OAAO,EAAE;IACtB,YAAA,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,EACnC,UAAU,CAAC,KAAK,CACjB,CAAC;IACH,SAAA;YACD,MAAM,UAAU,CAAC,KAAK,CAAC;SACxB;KACF,CAAC;IAEF;;IAEG;AACU,UAAA,gBAAgB,GAAG,IAAI,GAAG,CAGrC;QACA,CAAC,OAAO,EAAE,oBAAoB,CAAC;QAC/B,CAAC,OAAO,EAAE,oBAAoB,CAAC;IAChC,CAAA,EAAE;IAEH,SAAS,eAAe,CACtB,cAAmC,EACnC,MAAc,EAAA;IAEd,IAAA,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;IAC1C,QAAA,IAAI,MAAM,KAAK,aAAa,IAAI,aAAa,KAAK,GAAG,EAAE;IACrD,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;YACD,IAAI,aAAa,YAAY,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;IACjE,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;IACF,KAAA;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAEK,SAAU,MAAM,CACpB,GAAQ,EACR,EAAe,GAAA,UAAiB,EAChC,cAAA,GAAsC,CAAC,GAAG,CAAC,EAAA;QAE3C,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAgB,EAAA;IAC/D,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;gBACnB,OAAO;IACR,SAAA;YACD,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC/C,OAAO,CAAC,IAAI,CAAC,CAAA,gBAAA,EAAmB,EAAE,CAAC,MAAM,CAAqB,mBAAA,CAAA,CAAC,CAAC;gBAChE,OAAO;IACR,SAAA;IACD,QAAA,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAA,MAAA,CAAA,MAAA,CAAA,EACtB,IAAI,EAAE,EAAc,EAChB,EAAA,EAAE,CAAC,IAAgB,CACxB,CAAC;IACF,QAAA,MAAM,YAAY,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;IACrE,QAAA,IAAI,WAAW,CAAC;YAChB,IAAI;IACF,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAC5D,YAAA,QAAQ,IAAI;IACV,gBAAA,KAAA,KAAA;IACE,oBAAA;4BACE,WAAW,GAAG,QAAQ,CAAC;IACxB,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAA,KAAA;IACE,oBAAA;4BACE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACzD,WAAW,GAAG,IAAI,CAAC;IACpB,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAA,OAAA;IACE,oBAAA;4BACE,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACpD,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAA,WAAA;IACE,oBAAA;4BACE,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,CAAC;IAC5C,wBAAA,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5B,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAA,UAAA;IACE,oBAAA;4BACE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC;IAC9C,wBAAA,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;4BACnB,WAAW,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACxC,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAA,SAAA;IACE,oBAAA;4BACE,WAAW,GAAG,SAAS,CAAC;IACzB,qBAAA;wBACD,MAAM;IACR,gBAAA;wBACE,OAAO;IACV,aAAA;IACF,SAAA;IAAC,QAAA,OAAO,KAAK,EAAE;gBACd,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;IAC3C,SAAA;IACD,QAAA,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;IACzB,aAAA,KAAK,CAAC,CAAC,KAAK,KAAI;gBACf,OAAO,EAAE,KAAK,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;IACrC,SAAC,CAAC;IACD,aAAA,IAAI,CAAC,CAAC,WAAW,KAAI;gBACpB,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;gBAC5D,EAAE,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,KAAE,EAAE,EAAA,CAAA,EAAI,aAAa,CAAC,CAAC;gBACpD,IAAI,IAAI,0CAA0B;;IAEhC,gBAAA,EAAE,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAe,CAAC,CAAC;oBACnD,aAAa,CAAC,EAAE,CAAC,CAAC;oBAClB,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;IAC5D,oBAAA,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;IAClB,iBAAA;IACF,aAAA;IACH,SAAC,CAAC;IACD,aAAA,KAAK,CAAC,CAAC,KAAK,KAAI;;IAEf,YAAA,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC;IAC7C,gBAAA,KAAK,EAAE,IAAI,SAAS,CAAC,6BAA6B,CAAC;oBACnD,CAAC,WAAW,GAAG,CAAC;IACjB,aAAA,CAAC,CAAC;gBACH,EAAE,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,KAAE,EAAE,EAAA,CAAA,EAAI,aAAa,CAAC,CAAC;IACtD,SAAC,CAAC,CAAC;IACP,KAAQ,CAAC,CAAC;QACV,IAAI,EAAE,CAAC,KAAK,EAAE;YACZ,EAAE,CAAC,KAAK,EAAE,CAAC;IACZ,KAAA;IACH,CAAC;IAED,SAAS,aAAa,CAAC,QAAkB,EAAA;IACvC,IAAA,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC;IACrD,CAAC;IAED,SAAS,aAAa,CAAC,QAAkB,EAAA;QACvC,IAAI,aAAa,CAAC,QAAQ,CAAC;YAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAEe,SAAA,IAAI,CAAI,EAAY,EAAE,MAAY,EAAA;IAChD,IAAA,MAAM,gBAAgB,GAAyB,IAAI,GAAG,EAAE,CAAC;QAEzD,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,aAAa,CAAC,EAAS,EAAA;IAC7D,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,EAAkB,CAAC;IACpC,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACrB,OAAO;IACR,SAAA;YACD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;IACR,SAAA;YAED,IAAI;gBACF,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChB,SAAA;IAAS,gBAAA;IACR,YAAA,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,SAAA;IACH,KAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAI,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,CAAQ,CAAC;IACjE,CAAC;IAED,SAAS,oBAAoB,CAAC,UAAmB,EAAA;IAC/C,IAAA,IAAI,UAAU,EAAE;IACd,QAAA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAC/D,KAAA;IACH,CAAC;IAED,SAAS,eAAe,CAAC,EAAY,EAAA;IACnC,IAAA,OAAO,sBAAsB,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE;IAC3C,QAAA,IAAI,EAAqB,SAAA;IAC1B,KAAA,CAAC,CAAC,IAAI,CAAC,MAAK;YACX,aAAa,CAAC,EAAE,CAAC,CAAC;IACpB,KAAC,CAAC,CAAC;IACL,CAAC;IAaD,MAAM,YAAY,GAAG,IAAI,OAAO,EAAoB,CAAC;IACrD,MAAM,eAAe,GACnB,sBAAsB,IAAI,UAAU;IACpC,IAAA,IAAI,oBAAoB,CAAC,CAAC,EAAY,KAAI;IACxC,QAAA,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD,QAAA,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC/B,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAClB,eAAe,CAAC,EAAE,CAAC,CAAC;IACrB,SAAA;IACH,KAAC,CAAC,CAAC;IAEL,SAAS,aAAa,CAAC,KAAa,EAAE,EAAY,EAAA;IAChD,IAAA,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD,IAAA,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC/B,IAAA,IAAI,eAAe,EAAE;YACnB,eAAe,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IAC5C,KAAA;IACH,CAAC;IAED,SAAS,eAAe,CAAC,KAAa,EAAA;IACpC,IAAA,IAAI,eAAe,EAAE;IACnB,QAAA,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACnC,KAAA;IACH,CAAC;IAED,SAAS,WAAW,CAClB,EAAY,EACZ,gBAAqC,EACrC,IAAA,GAAqC,EAAE,EACvC,MAAiB,GAAA,YAAA,GAAc,EAAA;QAE/B,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;YAC9B,GAAG,CAAC,OAAO,EAAE,IAAI,EAAA;gBACf,oBAAoB,CAAC,eAAe,CAAC,CAAC;gBACtC,IAAI,IAAI,KAAK,YAAY,EAAE;IACzB,gBAAA,OAAO,MAAK;wBACV,eAAe,CAAC,KAAK,CAAC,CAAC;wBACvB,eAAe,CAAC,EAAE,CAAC,CAAC;wBACpB,gBAAgB,CAAC,KAAK,EAAE,CAAC;wBACzB,eAAe,GAAG,IAAI,CAAC;IACzB,iBAAC,CAAC;IACH,aAAA;gBACD,IAAI,IAAI,KAAK,MAAM,EAAE;IACnB,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,OAAO,EAAE,IAAI,EAAE,MAAM,KAAK,EAAE,CAAC;IAC9B,iBAAA;IACD,gBAAA,MAAM,CAAC,GAAG,sBAAsB,CAAC,EAAE,EAAE,gBAAgB,EAAE;IACrD,oBAAA,IAAI,EAAiB,KAAA;IACrB,oBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;IACpC,iBAAA,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACvB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvB,aAAA;IACD,YAAA,OAAO,WAAW,CAAC,EAAE,EAAE,gBAAgB,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;aAC3D;IACD,QAAA,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAA;gBACzB,oBAAoB,CAAC,eAAe,CAAC,CAAC;;;gBAGtC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACrD,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;IACE,gBAAA,IAAI,EAAiB,KAAA;IACrB,gBAAA,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC9C,KAAK;IACN,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAQ,CAAC;aAC9B;IACD,QAAA,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAA;gBACtC,oBAAoB,CAAC,eAAe,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnC,IAAK,IAAY,KAAK,cAAc,EAAE;IACpC,gBAAA,OAAO,sBAAsB,CAAC,EAAE,EAAE,gBAAgB,EAAE;IAClD,oBAAA,IAAI,EAAsB,UAAA;IAC3B,iBAAA,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACxB,aAAA;;gBAED,IAAI,IAAI,KAAK,MAAM,EAAE;IACnB,gBAAA,OAAO,WAAW,CAAC,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,aAAA;gBACD,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACxE,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;IACE,gBAAA,IAAI,EAAmB,OAAA;IACvB,gBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACnC,YAAY;IACb,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACvB;YACD,SAAS,CAAC,OAAO,EAAE,eAAe,EAAA;gBAChC,oBAAoB,CAAC,eAAe,CAAC,CAAC;gBACtC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACxE,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;IACE,gBAAA,IAAI,EAAuB,WAAA;IAC3B,gBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACnC,YAAY;IACb,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACvB;IACF,KAAA,CAAC,CAAC;IACH,IAAA,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACzB,IAAA,OAAO,KAAY,CAAC;IACtB,CAAC;IAED,SAAS,MAAM,CAAI,GAAgB,EAAA;IACjC,IAAA,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,gBAAgB,CAAC,YAAmB,EAAA;QAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChD,IAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,aAAa,GAAG,IAAI,OAAO,EAAuB,CAAC;IACzC,SAAA,QAAQ,CAAI,GAAM,EAAE,SAAyB,EAAA;IAC3D,IAAA,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAClC,IAAA,OAAO,GAAG,CAAC;IACb,CAAC;IAEK,SAAU,KAAK,CAAe,GAAM,EAAA;IACxC,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,WAAW,GAAG,IAAI,EAAE,CAAQ,CAAC;IAC5D,CAAC;IAEK,SAAU,cAAc,CAC5B,CAAwB,EACxB,UAAuB,UAAU,EACjC,YAAY,GAAG,GAAG,EAAA;QAElB,OAAO;IACL,QAAA,WAAW,EAAE,CAAC,GAAQ,EAAE,aAA6B,KACnD,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,aAAa,CAAC;YACjD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;YACxD,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC;SAC/D,CAAC;IACJ,CAAC;IAED,SAAS,WAAW,CAAC,KAAU,EAAA;QAC7B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,gBAAgB,EAAE;IAC9C,QAAA,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAC5B,YAAA,MAAM,CAAC,eAAe,EAAE,aAAa,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAClE,OAAO;IACL,gBAAA;IACE,oBAAA,IAAI,EAAuB,SAAA;wBAC3B,IAAI;IACJ,oBAAA,KAAK,EAAE,eAAe;IACvB,iBAAA;oBACD,aAAa;iBACd,CAAC;IACH,SAAA;IACF,KAAA;QACD,OAAO;IACL,QAAA;IACE,YAAA,IAAI,EAAmB,KAAA;gBACvB,KAAK;IACN,SAAA;IACD,QAAA,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;SAC/B,CAAC;IACJ,CAAC;IAED,SAAS,aAAa,CAAC,KAAgB,EAAA;QACrC,QAAQ,KAAK,CAAC,IAAI;IAChB,QAAA,KAAA,SAAA;IACE,YAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACpE,QAAA,KAAA,KAAA;gBACE,OAAO,KAAK,CAAC,KAAK,CAAC;IACtB,KAAA;IACH,CAAC;IAED,SAAS,sBAAsB,CAC7B,EAAY,EACZ,gBAAqC,EACrC,GAAY,EACZ,SAA0B,EAAA;IAE1B,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;IAC7B,QAAA,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC;IAC1B,QAAA,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAClC,IAAI,EAAE,CAAC,KAAK,EAAE;gBACZ,EAAE,CAAC,KAAK,EAAE,CAAC;IACZ,SAAA;YACD,EAAE,CAAC,WAAW,CAAG,MAAA,CAAA,MAAA,CAAA,EAAA,EAAE,IAAK,GAAG,CAAA,EAAI,SAAS,CAAC,CAAC;IAC9C,KAAC,CAAC,CAAC;IACH,CAAC;IAED,SAAS,YAAY,GAAA;IACnB,IAAA,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;aAChB,IAAI,CAAC,CAAC,CAAC;aACP,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aAC3E,IAAI,CAAC,GAAG,CAAC,CAAC;IACf;;;;;;;;;;;;;;;;;"}