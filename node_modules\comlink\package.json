{"name": "comlink", "version": "4.4.2", "description": "Comlink makes WebWorkers enjoyable", "main": "dist/umd/comlink.js", "module": "dist/esm/comlink.mjs", "types": "dist/umd/comlink.d.ts", "sideEffects": false, "scripts": {"build": "rollup -c", "test:unit": "karma start", "test:node": "mocha ./tests/node/main.mjs", "test:types": "tsc -p ./tests/tsconfig.json", "test:types:watch": "npm run test:types -- --watch", "test": "npm run fmt_test && npm run build && npm run test:types && npm run test:unit && npm run test:node", "fmt": "prettier --write './*.{mjs,js,ts,md,json,html}' './{src,docs,tests}/{,**/}*.{mjs,js,ts,md,json,html}'", "fmt_test": "test $(prettier -l './*.{mjs,js,ts,md,json,html}' './{src,docs,tests}/{**/,}*.{mjs,js,ts,md,json,html}' | wc -l) -eq 0", "watchtest": "CHROME_ONLY=1 karma start --no-single-run"}, "author": {"name": "Surma", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/comlink.git"}, "license": "Apache-2.0", "devDependencies": {"@rollup/plugin-terser": "0.4.0", "@rollup/plugin-typescript": "11.0.0", "chai": "^4.3.7", "conditional-type-checks": "1.0.6", "husky": "8.0.3", "karma": "6.4.1", "karma-chai": "0.1.0", "karma-chrome-launcher": "3.1.1", "karma-detect-browsers": "2.3.3", "karma-firefox-launcher": "2.1.2", "karma-mocha": "2.0.1", "karma-safari-launcher": "1.0.0", "karma-safaritechpreview-launcher": "2.0.2", "mocha": "10.2.0", "prettier": "2.8.3", "rimraf": "4.1.2", "rollup": "3.10.1", "tslib": "2.4.1", "typescript": "4.9.4"}}