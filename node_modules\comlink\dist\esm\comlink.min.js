/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
const e=Symbol("Comlink.proxy"),t=Symbol("Comlink.endpoint"),n=Symbol("Comlink.releaseProxy"),r=Symbol("Comlink.finalizer"),a=Symbol("Comlink.thrown"),s=e=>"object"==typeof e&&null!==e||"function"==typeof e,i=new Map([["proxy",{canHandle:t=>s(t)&&t[e],serialize(e){const{port1:t,port2:n}=new MessageChannel;return o(e,t),[n,[n]]},deserialize:e=>(e.start(),u(e))}],["throw",{canHandle:e=>s(e)&&a in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function o(e,t=globalThis,n=["*"]){t.addEventListener("message",(function s(i){if(!i||!i.data)return;if(!function(e,t){for(const n of e){if(t===n||"*"===n)return!0;if(n instanceof RegExp&&n.test(t))return!0}return!1}(n,i.origin))return void console.warn(`Invalid origin '${i.origin}' for comlink proxy`);const{id:u,type:l,path:p}=Object.assign({path:[]},i.data),f=(i.data.argumentList||[]).map(w);let g;try{const t=p.slice(0,-1).reduce(((e,t)=>e[t]),e),n=p.reduce(((e,t)=>e[t]),e);switch(l){case"GET":g=n;break;case"SET":t[p.slice(-1)[0]]=w(i.data.value),g=!0;break;case"APPLY":g=n.apply(t,f);break;case"CONSTRUCT":g=E(new n(...f));break;case"ENDPOINT":{const{port1:t,port2:n}=new MessageChannel;o(e,n),g=y(t,[t])}break;case"RELEASE":g=void 0;break;default:return}}catch(e){g={value:e,[a]:0}}Promise.resolve(g).catch((e=>({value:e,[a]:0}))).then((n=>{const[a,i]=v(n);t.postMessage(Object.assign(Object.assign({},a),{id:u}),i),"RELEASE"===l&&(t.removeEventListener("message",s),c(t),r in e&&"function"==typeof e[r]&&e[r]())})).catch((e=>{const[n,r]=v({value:new TypeError("Unserializable return value"),[a]:0});t.postMessage(Object.assign(Object.assign({},n),{id:u}),r)}))})),t.start&&t.start()}function c(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function u(e,t){const n=new Map;return e.addEventListener("message",(function(e){const{data:t}=e;if(!t||!t.id)return;const r=n.get(t.id);if(r)try{r(t)}finally{n.delete(t.id)}})),d(e,n,[],t)}function l(e){if(e)throw new Error("Proxy has been released and is not useable")}function p(e){return S(e,new Map,{type:"RELEASE"}).then((()=>{c(e)}))}const f=new WeakMap,g="FinalizationRegistry"in globalThis&&new FinalizationRegistry((e=>{const t=(f.get(e)||0)-1;f.set(e,t),0===t&&p(e)}));function d(e,r,a=[],s=function(){}){let i=!1;const o=new Proxy(s,{get(t,s){if(l(i),s===n)return()=>{!function(e){g&&g.unregister(e)}(o),p(e),r.clear(),i=!0};if("then"===s){if(0===a.length)return{then:()=>o};const t=S(e,r,{type:"GET",path:a.map((e=>e.toString()))}).then(w);return t.then.bind(t)}return d(e,r,[...a,s])},set(t,n,s){l(i);const[o,c]=v(s);return S(e,r,{type:"SET",path:[...a,n].map((e=>e.toString())),value:o},c).then(w)},apply(n,s,o){l(i);const c=a[a.length-1];if(c===t)return S(e,r,{type:"ENDPOINT"}).then(w);if("bind"===c)return d(e,r,a.slice(0,-1));const[u,p]=m(o);return S(e,r,{type:"APPLY",path:a.map((e=>e.toString())),argumentList:u},p).then(w)},construct(t,n){l(i);const[s,o]=m(n);return S(e,r,{type:"CONSTRUCT",path:a.map((e=>e.toString())),argumentList:s},o).then(w)}});return function(e,t){const n=(f.get(t)||0)+1;f.set(t,n),g&&g.register(e,t,e)}(o,e),o}function m(e){const t=e.map(v);return[t.map((e=>e[0])),(n=t.map((e=>e[1])),Array.prototype.concat.apply([],n))];var n}const h=new WeakMap;function y(e,t){return h.set(e,t),e}function E(t){return Object.assign(t,{[e]:!0})}function b(e,t=globalThis,n="*"){return{postMessage:(t,r)=>e.postMessage(t,n,r),addEventListener:t.addEventListener.bind(t),removeEventListener:t.removeEventListener.bind(t)}}function v(e){for(const[t,n]of i)if(n.canHandle(e)){const[r,a]=n.serialize(e);return[{type:"HANDLER",name:t,value:r},a]}return[{type:"RAW",value:e},h.get(e)||[]]}function w(e){switch(e.type){case"HANDLER":return i.get(e.name).deserialize(e.value);case"RAW":return e.value}}function S(e,t,n,r){return new Promise((a=>{const s=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");t.set(s,a),e.start&&e.start(),e.postMessage(Object.assign({id:s},n),r)}))}export{t as createEndpoint,o as expose,r as finalizer,E as proxy,e as proxyMarker,n as releaseProxy,y as transfer,i as transferHandlers,b as windowEndpoint,u as wrap};
//# sourceMappingURL=comlink.min.js.map
