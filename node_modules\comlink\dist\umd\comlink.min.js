!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Comlink={})}(this,(function(e){"use strict";
/**
     * @license
     * Copyright 2019 Google LLC
     * SPDX-License-Identifier: Apache-2.0
     */const t=Symbol("Comlink.proxy"),n=Symbol("Comlink.endpoint"),r=Symbol("Comlink.releaseProxy"),a=Symbol("Comlink.finalizer"),o=Symbol("Comlink.thrown"),s=e=>"object"==typeof e&&null!==e||"function"==typeof e,i=new Map([["proxy",{canHandle:e=>s(e)&&e[t],serialize(e){const{port1:t,port2:n}=new MessageChannel;return c(e,t),[n,[n]]},deserialize:e=>(e.start(),l(e))}],["throw",{canHandle:e=>s(e)&&o in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function c(e,t=globalThis,n=["*"]){t.addEventListener("message",(function r(s){if(!s||!s.data)return;if(!function(e,t){for(const n of e){if(t===n||"*"===n)return!0;if(n instanceof RegExp&&n.test(t))return!0}return!1}(n,s.origin))return void console.warn(`Invalid origin '${s.origin}' for comlink proxy`);const{id:i,type:l,path:p}=Object.assign({path:[]},s.data),f=(s.data.argumentList||[]).map(w);let d;try{const t=p.slice(0,-1).reduce(((e,t)=>e[t]),e),n=p.reduce(((e,t)=>e[t]),e);switch(l){case"GET":d=n;break;case"SET":t[p.slice(-1)[0]]=w(s.data.value),d=!0;break;case"APPLY":d=n.apply(t,f);break;case"CONSTRUCT":d=b(new n(...f));break;case"ENDPOINT":{const{port1:t,port2:n}=new MessageChannel;c(e,n),d=E(t,[t])}break;case"RELEASE":d=void 0;break;default:return}}catch(e){d={value:e,[o]:0}}Promise.resolve(d).catch((e=>({value:e,[o]:0}))).then((n=>{const[o,s]=v(n);t.postMessage(Object.assign(Object.assign({},o),{id:i}),s),"RELEASE"===l&&(t.removeEventListener("message",r),u(t),a in e&&"function"==typeof e[a]&&e[a]())})).catch((e=>{const[n,r]=v({value:new TypeError("Unserializable return value"),[o]:0});t.postMessage(Object.assign(Object.assign({},n),{id:i}),r)}))})),t.start&&t.start()}function u(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function l(e,t){const n=new Map;return e.addEventListener("message",(function(e){const{data:t}=e;if(!t||!t.id)return;const r=n.get(t.id);if(r)try{r(t)}finally{n.delete(t.id)}})),m(e,n,[],t)}function p(e){if(e)throw new Error("Proxy has been released and is not useable")}function f(e){return k(e,new Map,{type:"RELEASE"}).then((()=>{u(e)}))}const d=new WeakMap,g="FinalizationRegistry"in globalThis&&new FinalizationRegistry((e=>{const t=(d.get(e)||0)-1;d.set(e,t),0===t&&f(e)}));function m(e,t,a=[],o=function(){}){let s=!1;const i=new Proxy(o,{get(n,o){if(p(s),o===r)return()=>{!function(e){g&&g.unregister(e)}(i),f(e),t.clear(),s=!0};if("then"===o){if(0===a.length)return{then:()=>i};const n=k(e,t,{type:"GET",path:a.map((e=>e.toString()))}).then(w);return n.then.bind(n)}return m(e,t,[...a,o])},set(n,r,o){p(s);const[i,c]=v(o);return k(e,t,{type:"SET",path:[...a,r].map((e=>e.toString())),value:i},c).then(w)},apply(r,o,i){p(s);const c=a[a.length-1];if(c===n)return k(e,t,{type:"ENDPOINT"}).then(w);if("bind"===c)return m(e,t,a.slice(0,-1));const[u,l]=y(i);return k(e,t,{type:"APPLY",path:a.map((e=>e.toString())),argumentList:u},l).then(w)},construct(n,r){p(s);const[o,i]=y(r);return k(e,t,{type:"CONSTRUCT",path:a.map((e=>e.toString())),argumentList:o},i).then(w)}});return function(e,t){const n=(d.get(t)||0)+1;d.set(t,n),g&&g.register(e,t,e)}(i,e),i}function y(e){const t=e.map(v);return[t.map((e=>e[0])),(n=t.map((e=>e[1])),Array.prototype.concat.apply([],n))];var n}const h=new WeakMap;function E(e,t){return h.set(e,t),e}function b(e){return Object.assign(e,{[t]:!0})}function v(e){for(const[t,n]of i)if(n.canHandle(e)){const[r,a]=n.serialize(e);return[{type:"HANDLER",name:t,value:r},a]}return[{type:"RAW",value:e},h.get(e)||[]]}function w(e){switch(e.type){case"HANDLER":return i.get(e.name).deserialize(e.value);case"RAW":return e.value}}function k(e,t,n,r){return new Promise((a=>{const o=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");t.set(o,a),e.start&&e.start(),e.postMessage(Object.assign({id:o},n),r)}))}e.createEndpoint=n,e.expose=c,e.finalizer=a,e.proxy=b,e.proxyMarker=t,e.releaseProxy=r,e.transfer=E,e.transferHandlers=i,e.windowEndpoint=function(e,t=globalThis,n="*"){return{postMessage:(t,r)=>e.postMessage(t,n,r),addEventListener:t.addEventListener.bind(t),removeEventListener:t.removeEventListener.bind(t)}},e.wrap=l}));
//# sourceMappingURL=comlink.min.js.map
