# 🚀 Deployment Guide - Crypto Leader Quiz Mini App

## 📋 Pre-Deployment Checklist

Before deploying, make sure you have:
- [x] Tested locally on `http://localhost:3000`
- [x] All images load correctly from `Gambar/` folder
- [x] Quiz logic works (8 questions → results)
- [x] All 5 character results display properly
- [x] Share functionality works
- [x] Mobile responsive design
- [x] Mini App SDK initializes without errors

## 🌐 Quick Deploy Options

### 1. Vercel (Recommended - 2 minutes)

**Step 1: Push to GitHub**
```bash
git init
git add .
git commit -m "🧠 Crypto Leader Quiz Mini App - Ready for deployment"
git branch -M main
git remote add origin https://github.com/YOURUSERNAME/crypto-leader-quiz.git
git push -u origin main
```

**Step 2: Deploy to Vercel**
1. Go to [vercel.com](https://vercel.com) and sign in with GitHub
2. Click "New Project"
3. Import your `crypto-leader-quiz` repository
4. Vercel auto-detects settings from `vercel.json`
5. Click "Deploy" 
6. Get your live URL: `https://crypto-leader-quiz-RANDOM.vercel.app`

**Step 3: Test Deployment**
```bash
curl -I https://your-vercel-url.vercel.app
```

### 2. Netlify (Drag & Drop - 1 minute)

**Option A: Drag & Drop**
1. Go to [netlify.com](https://netlify.com)
2. Drag your entire project folder to the deploy area
3. Get instant URL: `https://RANDOM-NAME.netlify.app`

**Option B: GitHub Integration**
1. Push to GitHub (same as Vercel step 1)
2. Connect repository to Netlify
3. Netlify reads `netlify.toml` automatically
4. Auto-deploy on every push

### 3. GitHub Pages (Free hosting)

```bash
# Create gh-pages branch
git checkout -b gh-pages
git push origin gh-pages

# Enable in repository settings
# Settings → Pages → Source: gh-pages branch
# URL: https://YOURUSERNAME.github.io/crypto-leader-quiz
```

## 🔧 Farcaster Integration

### Enable Developer Mode
1. Go to: https://farcaster.xyz/~/settings/developer-tools
2. Toggle "Developer Mode" ON
3. Access developer tools on desktop

### Test Your Mini App
1. **Local Testing:**
   ```bash
   # Use ngrok to expose local server
   npx ngrok http 3000
   # Test the ngrok URL in Farcaster
   ```

2. **Production Testing:**
   - Use your deployed URL
   - Test in Farcaster mobile app
   - Verify all functionality works

### Submit to Mini App Directory
1. Go to Farcaster Developer Tools
2. Create new Mini App manifest
3. Fill in details:
   - **Name:** "Which Crypto Leader Are You?"
   - **Description:** "Discover your Web3 personality through 8 questions and match with famous crypto leaders!"
   - **URL:** Your deployed URL
   - **Category:** Entertainment, Education
   - **Keywords:** quiz, personality, crypto, web3

## 🐛 Common Deployment Issues

### Issue: Images not loading
**Solution:**
```bash
# Check image paths are correct
curl https://your-url.com/Gambar/satoshi.png -I
# Should return 200 OK
```

### Issue: Mini App SDK not working
**Solution:**
- Ensure HTTPS deployment (required for Mini Apps)
- Check browser console for errors
- Verify SDK import in script.js

### Issue: Share functionality broken
**Solution:**
- Test on actual mobile device
- Check clipboard permissions
- Verify share text formatting

### Issue: CORS errors
**Solution:**
- Vercel: `vercel.json` handles this
- Netlify: `netlify.toml` handles this
- GitHub Pages: Add headers in repository settings

## 📊 Performance Optimization

### Before Deployment:
```bash
# Optimize images (optional)
# Use tools like TinyPNG or ImageOptim

# Minify CSS/JS (optional for this simple app)
# The app is already optimized for fast loading
```

### After Deployment:
- Test loading speed on mobile
- Check Lighthouse scores
- Monitor user engagement

## 🎯 Success Metrics

Track these after deployment:
- [ ] App loads in < 2 seconds
- [ ] Quiz completion rate > 80%
- [ ] Share rate > 30%
- [ ] Mobile experience is smooth
- [ ] No console errors

## 🔄 Updates & Maintenance

### To update your Mini App:
1. Make changes locally
2. Test thoroughly
3. Push to GitHub
4. Auto-deploy (Vercel/Netlify) or manual deploy (GitHub Pages)

### Version Control:
```bash
# Tag releases
git tag -a v1.0.0 -m "Initial release"
git push origin v1.0.0

# For updates
git tag -a v1.1.0 -m "Added new questions"
git push origin v1.1.0
```

## 🎉 Go Live!

Once deployed and tested:
1. Share your Mini App URL in Farcaster
2. Post in `/fc-devs` channel for feedback
3. Submit to Mini App directories
4. Monitor usage and iterate

**Your Mini App is now live and ready for users! 🚀**

---

**Need help?** Join the Farcaster developer community:
- Channel: [/fc-devs](https://warpcast.com/~/channel/fc-devs)
- Docs: [miniapps.farcaster.xyz](https://miniapps.farcaster.xyz)
- Support: [Farcaster Discord](https://discord.gg/farcaster)
