{"version": 3, "file": "comlink.js", "sources": ["../../src/comlink.ts"], "sourcesContent": [null], "names": [], "mappings": "AAAA;;;;AAIG;MAaU,WAAW,GAAG,MAAM,CAAC,eAAe,EAAE;MACtC,cAAc,GAAG,MAAM,CAAC,kBAAkB,EAAE;MAC5C,YAAY,GAAG,MAAM,CAAC,sBAAsB,EAAE;MAC9C,SAAS,GAAG,MAAM,CAAC,mBAAmB,EAAE;AAErD,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAuJ7C,MAAM,QAAQ,GAAG,CAAC,GAAY,KAC5B,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,UAAU,CAAC;AA+BzE;;AAEG;AACH,MAAM,oBAAoB,GAAyC;AACjE,IAAA,SAAS,EAAE,CAAC,GAAG,KACb,QAAQ,CAAC,GAAG,CAAC,IAAK,GAAmB,CAAC,WAAW,CAAC;AACpD,IAAA,SAAS,CAAC,GAAG,EAAA;QACX,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC;AAC9C,QAAA,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACnB,QAAA,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;KACzB;AACD,IAAA,WAAW,CAAC,IAAI,EAAA;QACd,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;CACF,CAAC;AAcF;;AAEG;AACH,MAAM,oBAAoB,GAGtB;AACF,IAAA,SAAS,EAAE,CAAC,KAAK,KACf,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,IAAI,KAAK;IACzC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAA;AACjB,QAAA,IAAI,UAAiC,CAAC;QACtC,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,YAAA,UAAU,GAAG;AACX,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;AACnB,iBAAA;aACF,CAAC;AACH,SAAA;AAAM,aAAA;YACL,UAAU,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AACxC,SAAA;AACD,QAAA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;KACzB;AACD,IAAA,WAAW,CAAC,UAAU,EAAA;QACpB,IAAI,UAAU,CAAC,OAAO,EAAE;AACtB,YAAA,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,EACnC,UAAU,CAAC,KAAK,CACjB,CAAC;AACH,SAAA;QACD,MAAM,UAAU,CAAC,KAAK,CAAC;KACxB;CACF,CAAC;AAEF;;AAEG;AACU,MAAA,gBAAgB,GAAG,IAAI,GAAG,CAGrC;IACA,CAAC,OAAO,EAAE,oBAAoB,CAAC;IAC/B,CAAC,OAAO,EAAE,oBAAoB,CAAC;AAChC,CAAA,EAAE;AAEH,SAAS,eAAe,CACtB,cAAmC,EACnC,MAAc,EAAA;AAEd,IAAA,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;AAC1C,QAAA,IAAI,MAAM,KAAK,aAAa,IAAI,aAAa,KAAK,GAAG,EAAE;AACrD,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACD,IAAI,aAAa,YAAY,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjE,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,MAAM,CACpB,GAAQ,EACR,EAAe,GAAA,UAAiB,EAChC,cAAA,GAAsC,CAAC,GAAG,CAAC,EAAA;IAE3C,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAgB,EAAA;AAC/D,QAAA,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;YACnB,OAAO;AACR,SAAA;QACD,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE;YAC/C,OAAO,CAAC,IAAI,CAAC,CAAA,gBAAA,EAAmB,EAAE,CAAC,MAAM,CAAqB,mBAAA,CAAA,CAAC,CAAC;YAChE,OAAO;AACR,SAAA;AACD,QAAA,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAA,MAAA,CAAA,MAAA,CAAA,EACtB,IAAI,EAAE,EAAc,EAChB,EAAA,EAAE,CAAC,IAAgB,CACxB,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;AACrE,QAAA,IAAI,WAAW,CAAC;QAChB,IAAI;AACF,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5D,YAAA,QAAQ,IAAI;AACV,gBAAA,KAAA,KAAA;AACE,oBAAA;wBACE,WAAW,GAAG,QAAQ,CAAC;AACxB,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAA,KAAA;AACE,oBAAA;wBACE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACzD,WAAW,GAAG,IAAI,CAAC;AACpB,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAA,OAAA;AACE,oBAAA;wBACE,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AACpD,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAA,WAAA;AACE,oBAAA;wBACE,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,CAAC;AAC5C,wBAAA,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAA,UAAA;AACE,oBAAA;wBACE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC;AAC9C,wBAAA,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;wBACnB,WAAW,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAA,SAAA;AACE,oBAAA;wBACE,WAAW,GAAG,SAAS,CAAC;AACzB,qBAAA;oBACD,MAAM;AACR,gBAAA;oBACE,OAAO;AACV,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACd,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AAC3C,SAAA;AACD,QAAA,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;AACzB,aAAA,KAAK,CAAC,CAAC,KAAK,KAAI;YACf,OAAO,EAAE,KAAK,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;AACrC,SAAC,CAAC;AACD,aAAA,IAAI,CAAC,CAAC,WAAW,KAAI;YACpB,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;YAC5D,EAAE,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,KAAE,EAAE,EAAA,CAAA,EAAI,aAAa,CAAC,CAAC;YACpD,IAAI,IAAI,0CAA0B;;AAEhC,gBAAA,EAAE,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAe,CAAC,CAAC;gBACnD,aAAa,CAAC,EAAE,CAAC,CAAC;gBAClB,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;AAC5D,oBAAA,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;AAClB,iBAAA;AACF,aAAA;AACH,SAAC,CAAC;AACD,aAAA,KAAK,CAAC,CAAC,KAAK,KAAI;;AAEf,YAAA,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC;AAC7C,gBAAA,KAAK,EAAE,IAAI,SAAS,CAAC,6BAA6B,CAAC;gBACnD,CAAC,WAAW,GAAG,CAAC;AACjB,aAAA,CAAC,CAAC;YACH,EAAE,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,KAAE,EAAE,EAAA,CAAA,EAAI,aAAa,CAAC,CAAC;AACtD,SAAC,CAAC,CAAC;AACP,KAAQ,CAAC,CAAC;IACV,IAAI,EAAE,CAAC,KAAK,EAAE;QACZ,EAAE,CAAC,KAAK,EAAE,CAAC;AACZ,KAAA;AACH,CAAC;AAED,SAAS,aAAa,CAAC,QAAkB,EAAA;AACvC,IAAA,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC;AACrD,CAAC;AAED,SAAS,aAAa,CAAC,QAAkB,EAAA;IACvC,IAAI,aAAa,CAAC,QAAQ,CAAC;QAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;AAChD,CAAC;AAEe,SAAA,IAAI,CAAI,EAAY,EAAE,MAAY,EAAA;AAChD,IAAA,MAAM,gBAAgB,GAAyB,IAAI,GAAG,EAAE,CAAC;IAEzD,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,aAAa,CAAC,EAAS,EAAA;AAC7D,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,EAAkB,CAAC;AACpC,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACrB,OAAO;AACR,SAAA;QACD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO;AACR,SAAA;QAED,IAAI;YACF,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChB,SAAA;AAAS,gBAAA;AACR,YAAA,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,SAAA;AACH,KAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAI,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,CAAQ,CAAC;AACjE,CAAC;AAED,SAAS,oBAAoB,CAAC,UAAmB,EAAA;AAC/C,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;AAC/D,KAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,EAAY,EAAA;AACnC,IAAA,OAAO,sBAAsB,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE;AAC3C,QAAA,IAAI,EAAqB,SAAA;AAC1B,KAAA,CAAC,CAAC,IAAI,CAAC,MAAK;QACX,aAAa,CAAC,EAAE,CAAC,CAAC;AACpB,KAAC,CAAC,CAAC;AACL,CAAC;AAaD,MAAM,YAAY,GAAG,IAAI,OAAO,EAAoB,CAAC;AACrD,MAAM,eAAe,GACnB,sBAAsB,IAAI,UAAU;AACpC,IAAA,IAAI,oBAAoB,CAAC,CAAC,EAAY,KAAI;AACxC,QAAA,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,QAAA,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC/B,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,eAAe,CAAC,EAAE,CAAC,CAAC;AACrB,SAAA;AACH,KAAC,CAAC,CAAC;AAEL,SAAS,aAAa,CAAC,KAAa,EAAE,EAAY,EAAA;AAChD,IAAA,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,IAAA,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC/B,IAAA,IAAI,eAAe,EAAE;QACnB,eAAe,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAC5C,KAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,KAAa,EAAA;AACpC,IAAA,IAAI,eAAe,EAAE;AACnB,QAAA,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,WAAW,CAClB,EAAY,EACZ,gBAAqC,EACrC,IAAA,GAAqC,EAAE,EACvC,MAAiB,GAAA,YAAA,GAAc,EAAA;IAE/B,IAAI,eAAe,GAAG,KAAK,CAAC;AAC5B,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;QAC9B,GAAG,CAAC,OAAO,EAAE,IAAI,EAAA;YACf,oBAAoB,CAAC,eAAe,CAAC,CAAC;YACtC,IAAI,IAAI,KAAK,YAAY,EAAE;AACzB,gBAAA,OAAO,MAAK;oBACV,eAAe,CAAC,KAAK,CAAC,CAAC;oBACvB,eAAe,CAAC,EAAE,CAAC,CAAC;oBACpB,gBAAgB,CAAC,KAAK,EAAE,CAAC;oBACzB,eAAe,GAAG,IAAI,CAAC;AACzB,iBAAC,CAAC;AACH,aAAA;YACD,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,OAAO,EAAE,IAAI,EAAE,MAAM,KAAK,EAAE,CAAC;AAC9B,iBAAA;AACD,gBAAA,MAAM,CAAC,GAAG,sBAAsB,CAAC,EAAE,EAAE,gBAAgB,EAAE;AACrD,oBAAA,IAAI,EAAiB,KAAA;AACrB,oBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AACpC,iBAAA,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,aAAA;AACD,YAAA,OAAO,WAAW,CAAC,EAAE,EAAE,gBAAgB,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAC3D;AACD,QAAA,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAA;YACzB,oBAAoB,CAAC,eAAe,CAAC,CAAC;;;YAGtC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;AACrD,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;AACE,gBAAA,IAAI,EAAiB,KAAA;AACrB,gBAAA,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC9C,KAAK;AACN,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAQ,CAAC;SAC9B;AACD,QAAA,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAA;YACtC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnC,IAAK,IAAY,KAAK,cAAc,EAAE;AACpC,gBAAA,OAAO,sBAAsB,CAAC,EAAE,EAAE,gBAAgB,EAAE;AAClD,oBAAA,IAAI,EAAsB,UAAA;AAC3B,iBAAA,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACxB,aAAA;;YAED,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,gBAAA,OAAO,WAAW,CAAC,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,aAAA;YACD,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACxE,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;AACE,gBAAA,IAAI,EAAmB,OAAA;AACvB,gBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnC,YAAY;AACb,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACvB;QACD,SAAS,CAAC,OAAO,EAAE,eAAe,EAAA;YAChC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACxE,YAAA,OAAO,sBAAsB,CAC3B,EAAE,EACF,gBAAgB,EAChB;AACE,gBAAA,IAAI,EAAuB,WAAA;AAC3B,gBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnC,YAAY;AACb,aAAA,EACD,aAAa,CACd,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACvB;AACF,KAAA,CAAC,CAAC;AACH,IAAA,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACzB,IAAA,OAAO,KAAY,CAAC;AACtB,CAAC;AAED,SAAS,MAAM,CAAI,GAAgB,EAAA;AACjC,IAAA,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAC,YAAmB,EAAA;IAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAChD,IAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,aAAa,GAAG,IAAI,OAAO,EAAuB,CAAC;AACzC,SAAA,QAAQ,CAAI,GAAM,EAAE,SAAyB,EAAA;AAC3D,IAAA,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAClC,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAEK,SAAU,KAAK,CAAe,GAAM,EAAA;AACxC,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,WAAW,GAAG,IAAI,EAAE,CAAQ,CAAC;AAC5D,CAAC;AAEK,SAAU,cAAc,CAC5B,CAAwB,EACxB,UAAuB,UAAU,EACjC,YAAY,GAAG,GAAG,EAAA;IAElB,OAAO;AACL,QAAA,WAAW,EAAE,CAAC,GAAQ,EAAE,aAA6B,KACnD,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,aAAa,CAAC;QACjD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;QACxD,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC;KAC/D,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,KAAU,EAAA;IAC7B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,gBAAgB,EAAE;AAC9C,QAAA,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,MAAM,CAAC,eAAe,EAAE,aAAa,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAClE,OAAO;AACL,gBAAA;AACE,oBAAA,IAAI,EAAuB,SAAA;oBAC3B,IAAI;AACJ,oBAAA,KAAK,EAAE,eAAe;AACvB,iBAAA;gBACD,aAAa;aACd,CAAC;AACH,SAAA;AACF,KAAA;IACD,OAAO;AACL,QAAA;AACE,YAAA,IAAI,EAAmB,KAAA;YACvB,KAAK;AACN,SAAA;AACD,QAAA,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;KAC/B,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,KAAgB,EAAA;IACrC,QAAQ,KAAK,CAAC,IAAI;AAChB,QAAA,KAAA,SAAA;AACE,YAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpE,QAAA,KAAA,KAAA;YACE,OAAO,KAAK,CAAC,KAAK,CAAC;AACtB,KAAA;AACH,CAAC;AAED,SAAS,sBAAsB,CAC7B,EAAY,EACZ,gBAAqC,EACrC,GAAY,EACZ,SAA0B,EAAA;AAE1B,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,QAAA,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC;AAC1B,QAAA,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAClC,IAAI,EAAE,CAAC,KAAK,EAAE;YACZ,EAAE,CAAC,KAAK,EAAE,CAAC;AACZ,SAAA;QACD,EAAE,CAAC,WAAW,CAAG,MAAA,CAAA,MAAA,CAAA,EAAA,EAAE,IAAK,GAAG,CAAA,EAAI,SAAS,CAAC,CAAC;AAC9C,KAAC,CAAC,CAAC;AACH,CAAC;AAED,SAAS,YAAY,GAAA;AACnB,IAAA,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;SAChB,IAAI,CAAC,CAAC,CAAC;SACP,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAC3E,IAAI,CAAC,GAAG,CAAC,CAAC;AACf;;;;"}