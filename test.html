<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .pass { color: green; }
        .fail { color: red; }
        .test-result { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 Crypto Leader Quiz - Test Results</h1>
    
    <div class="test-section">
        <h2>📁 File Structure Test</h2>
        <div id="file-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>🖼️ Image Loading Test</h2>
        <div id="image-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>📊 Quiz Logic Test</h2>
        <div id="quiz-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Character Results Test</h2>
        <div id="character-tests"></div>
    </div>

    <script>
        // Test Results Container
        const fileTests = document.getElementById('file-tests');
        const imageTests = document.getElementById('image-tests');
        const quizTests = document.getElementById('quiz-tests');
        const characterTests = document.getElementById('character-tests');

        function addTestResult(container, testName, passed, details = '') {
            const div = document.createElement('div');
            div.className = 'test-result';
            div.innerHTML = `
                <span class="${passed ? 'pass' : 'fail'}">
                    ${passed ? '✅' : '❌'} ${testName}
                </span>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            container.appendChild(div);
        }

        // Test 1: File Structure
        async function testFileStructure() {
            const files = ['index.html', 'styles.css', 'script.js', 'manifest.json', 'package.json'];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    addTestResult(fileTests, `${file} exists`, response.ok);
                } catch (error) {
                    addTestResult(fileTests, `${file} exists`, false, error.message);
                }
            }
        }

        // Test 2: Image Loading
        async function testImages() {
            const images = ['satoshi.png', 'vitalik.png', 'cz.png', 'brian.png', 'dorsey.png'];
            
            for (const image of images) {
                try {
                    const response = await fetch(`Gambar/${image}`, { method: 'HEAD' });
                    addTestResult(imageTests, `${image} loads`, response.ok);
                } catch (error) {
                    addTestResult(imageTests, `${image} loads`, false, error.message);
                }
            }
        }

        // Test 3: Quiz Logic (Import and test)
        async function testQuizLogic() {
            try {
                // Test if script.js can be loaded
                const response = await fetch('script.js');
                const scriptContent = await response.text();
                
                addTestResult(quizTests, 'Script.js loads', response.ok);
                addTestResult(quizTests, 'Contains questions array', scriptContent.includes('const questions'));
                addTestResult(quizTests, 'Contains characters object', scriptContent.includes('const characters'));
                addTestResult(quizTests, 'Has 8 questions', (scriptContent.match(/question:/g) || []).length === 8);
                addTestResult(quizTests, 'Has Mini App SDK import', scriptContent.includes('@farcaster/miniapp-sdk'));
                
            } catch (error) {
                addTestResult(quizTests, 'Quiz logic test', false, error.message);
            }
        }

        // Test 4: Character Results
        async function testCharacters() {
            const expectedCharacters = ['satoshi', 'vitalik', 'cz', 'brian', 'dorsey'];
            
            try {
                const response = await fetch('script.js');
                const scriptContent = await response.text();
                
                for (const char of expectedCharacters) {
                    const hasCharacter = scriptContent.includes(`${char}:`);
                    addTestResult(characterTests, `${char} character defined`, hasCharacter);
                }
                
                // Test if all characters have required properties
                const hasName = scriptContent.includes('name:');
                const hasSubtitle = scriptContent.includes('subtitle:');
                const hasTraits = scriptContent.includes('traits:');
                const hasDescription = scriptContent.includes('description:');
                const hasQuote = scriptContent.includes('quote:');
                const hasImage = scriptContent.includes('image:');
                const hasShareText = scriptContent.includes('shareText:');
                
                addTestResult(characterTests, 'All characters have name', hasName);
                addTestResult(characterTests, 'All characters have subtitle', hasSubtitle);
                addTestResult(characterTests, 'All characters have traits', hasTraits);
                addTestResult(characterTests, 'All characters have description', hasDescription);
                addTestResult(characterTests, 'All characters have quote', hasQuote);
                addTestResult(characterTests, 'All characters have image', hasImage);
                addTestResult(characterTests, 'All characters have shareText', hasShareText);
                
            } catch (error) {
                addTestResult(characterTests, 'Character test', false, error.message);
            }
        }

        // Run all tests
        async function runAllTests() {
            console.log('🧪 Running all tests...');
            await testFileStructure();
            await testImages();
            await testQuizLogic();
            await testCharacters();
            console.log('✅ All tests completed!');
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
