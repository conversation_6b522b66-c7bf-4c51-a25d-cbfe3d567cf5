* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 400px;
    margin: 0 auto;
    min-height: 100vh;
    position: relative;
}

.screen {
    display: none;
    padding: 20px;
    min-height: 100vh;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.screen.active {
    display: flex;
}

/* Start Screen */
.hero {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.hero h1 {
    font-size: 28px;
    font-weight: 800;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.leader-preview {
    margin: 30px 0;
}

.preview-avatars {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.avatar-mini {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preview-text {
    font-size: 14px;
    color: #888;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #333;
    border: 2px solid #ddd;
    padding: 12px 25px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 10px;
}

/* Quiz Screen */
.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.question-counter {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 30px;
}

.question-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    width: 100%;
}

.question-container h2 {
    font-size: 20px;
    margin-bottom: 25px;
    line-height: 1.4;
    color: #333;
}

.answers {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.answer-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.2);
    padding: 15px 20px;
    border-radius: 12px;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    line-height: 1.4;
}

.answer-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

/* Result Screen */
.result-container {
    width: 100%;
    text-align: center;
}

.result-header h2 {
    color: white;
    font-size: 24px;
    margin-bottom: 20px;
}

.result-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.result-image {
    display: flex;
    height: 250px;
    overflow: hidden;
}

.result-image img {
    width: 40%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px 0 0 0;
}

.result-overlay {
    flex: 1;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left;
}

.result-name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #ecf0f1;
}

.result-subtitle {
    font-size: 14px;
    color: #bdc3c7;
    margin-bottom: 12px;
}

.result-traits {
    font-size: 12px;
    color: #95a5a6;
    margin-bottom: 15px;
    line-height: 1.4;
}

.result-quote {
    font-size: 13px;
    font-style: italic;
    color: #e8f4fd;
    border-left: 3px solid #3498db;
    padding-left: 10px;
}

.result-description {
    padding: 25px;
}

.result-description p {
    font-size: 16px;
    line-height: 1.5;
    color: #555;
}

.result-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

@media (max-width: 480px) {
    .container {
        max-width: 100%;
    }
    
    .screen {
        padding: 15px;
    }
    
    .hero {
        padding: 30px 20px;
    }
    
    .hero h1 {
        font-size: 24px;
    }
}
